document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在查询工具页面的密码生成器选项卡
    const isInQueryPage = document.getElementById('generator-tab') !== null;
    
    if (!isInQueryPage) {
        console.log('不在查询工具页面的密码生成器选项卡，跳过初始化');
        return;
    }
    
    console.log('初始化查询工具页面中的密码生成器选项卡');
    
    // 初始化标签页
    if (typeof bootstrap !== 'undefined') {
        console.log('使用Bootstrap初始化标签页');
        const triggerTabList = document.querySelectorAll('#queryTabs button');
        triggerTabList.forEach(triggerEl => {
            const tabTrigger = new bootstrap.Tab(triggerEl);
            triggerEl.addEventListener('click', event => {
                event.preventDefault();
                tabTrigger.show();
            });
        });
    } else {
        console.error('Bootstrap未加载，无法初始化标签页');
    }
    
    // 获取DOM元素
    const quotaInput = document.getElementById('gen_quota');
    const expiresInput = document.getElementById('gen_expires');
    const countInput = document.getElementById('gen_count');
    const downloadInput = document.getElementById('gen_download');
    const generateBtn = document.getElementById('gen_generateBtn');
    const copyBtn = document.getElementById('gen_copyBtn');
    const clearBtn = document.getElementById('gen_clearBtn');
    const downloadBtn = document.getElementById('gen_downloadBtn');
    const adminModeBtn = document.getElementById('gen_adminModeBtn');
    const resultArea = document.getElementById('gen_result');
    const progressContainer = document.getElementById('gen_progressContainer');
    const progressBar = document.getElementById('gen_progressBar');
    const linkButton = document.getElementById('gen_linkButton');
    const statusMessage = document.getElementById('statusMessage');
    
    // 获取应用根路径
    function getAppRoot() {
        // 从当前URL中获取应用根路径
        const pathParts = window.location.pathname.split('/');
        if (pathParts.length >= 2 && pathParts[1] === 'chaxun') {
            return '/chaxun';
        }
        return '';
    }
    
    const APP_ROOT = getAppRoot();
    console.log('应用根路径:', APP_ROOT);
    
    // 存储生成的密钥
    let generatedKeys = [];
    
    // 管理模式状态
    let isAdminMode = false;
    
    // 配额快捷按钮
    document.querySelectorAll('.gen-quick-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const value = parseInt(this.dataset.value);
            quotaInput.value = value;
            
            // 如果联动开启，设置对应的有效期
            if (linkButton.dataset.linked === 'true') {
                // 配额与有效期的对应关系
                const quotaToExpires = {
                    4: 1,   // 4次对应1天
                    7: 7,   // 7次对应7天
                    30: 30  // 30次对应30天
                };
                
                // 如果当前配额在映射表中，设置对应的有效期
                if (quotaToExpires[value]) {
                    expiresInput.value = quotaToExpires[value];
                }
            }
        });
    });
    
    // 有效期快捷按钮
    document.querySelectorAll('.gen-quick-btn-expires').forEach(btn => {
        btn.addEventListener('click', function() {
            const value = parseInt(this.dataset.value);
            expiresInput.value = value;
            
            // 如果联动开启，设置对应的配额
            if (linkButton.dataset.linked === 'true') {
                // 有效期与配额的对应关系
                const expiresToQuota = {
                    1: 4,   // 1天对应4次
                    7: 7,   // 7天对应7次
                    30: 30  // 30天对应30次
                };
                
                // 如果当前有效期在映射表中，设置对应的配额
                if (expiresToQuota[value]) {
                    quotaInput.value = expiresToQuota[value];
                }
            }
        });
    });
    
    // 联动按钮
    linkButton.addEventListener('click', function() {
        const isLinked = this.dataset.linked === 'true';
        this.dataset.linked = isLinked ? 'false' : 'true';
        this.classList.toggle('btn-success', !isLinked);
        this.classList.toggle('btn-secondary', isLinked);
    });
    
    // 生成卡密按钮
    generateBtn.addEventListener('click', function() {
        // 获取输入值
        const quota = parseInt(quotaInput.value);
        const expires = parseInt(expiresInput.value);
        const count = parseInt(countInput.value);
        const downloadLink = downloadInput.value;
        
        // 验证输入
        if (isNaN(quota) || quota < 1 || quota > 1000000) {
            showMessage('配额必须在1到1000000之间', 'error');
            return;
        }
        
        if (isNaN(expires) || expires < 0 || expires > 3650) {
            showMessage('有效期必须在0到3650天之间', 'error');
            return;
        }
        
        if (isNaN(count) || count < 1 || count > 1000) {
            showMessage('生成数量必须在1到1000之间', 'error');
            return;
        }
        
        // 显示进度条
        progressContainer.style.display = 'block';
        progressBar.style.width = '0%';
        
        // 禁用按钮
        generateBtn.disabled = true;
        
        // 清空结果区域
        resultArea.value = '';
        
        // 准备请求数据
        const data = {
            quota: quota,
            expires_days: expires,
            count: count
        };
        
        // 发送请求
        fetch(`${APP_ROOT}/api/generate-keys`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('网络响应不正常');
            }
            return response.json();
        })
        .then(data => {
            if (data.error) {
                throw new Error(data.error);
            }
            
            // 存储生成的密钥
            generatedKeys = data.keys;
            
            // 格式化输出
            let resultText;
            
            if (isAdminMode) {
                // 管理模式下的显示格式
                let expiresText = expires === 0 ? "永久" : `${expires}天`;
                resultText = `${expiresText}_${count}个_${quota}次\n`;
                
                // 只显示卡密信息，一行一个
                data.keys.forEach((key) => {
                    resultText += `${key.key_hash}\n`;
                });
            } else {
                // 普通模式下的显示格式
                const currentTime = convertToChineseTime(data.timestamp);
                
                resultText = `=== 卡密生成记录 - ${currentTime} ===\n\n`;
                
                // 有效期显示处理
                let expiresText = expires === 0 ? "永久" : `${expires}天`;
                
                resultText += `总配额: ${quota}\n`;
                resultText += `有效期: ${expiresText}\n`;
                resultText += `生成数量: ${count}\n`;
                resultText += "-".repeat(50) + "\n\n";
                
                // 写入卡密信息
                data.keys.forEach((key, index) => {
                    resultText += `【卡密 ${index + 1}】\n`;
                    resultText += `卡密: ${key.key_hash}\n`;
                    resultText += `过期时间: ${convertToChineseTime(key.expires_at)}\n`;
                    resultText += `总配额: ${key.total_quota}\n`;
                    resultText += `剩余配额: ${key.remaining}\n\n`;
                });
                
                // 写入备注信息
                resultText += "=== 备注信息 ===\n";
                resultText += " 请妥善保管原始卡密\n\n";
                resultText += `软件下载地址：\n${downloadLink}\n`;
                resultText += "=".repeat(50);
            }
            
            // 显示结果
            resultArea.value = resultText;
            
            // 启用复制、清空和管理模式按钮
            copyBtn.disabled = false;
            clearBtn.disabled = false;
            downloadBtn.disabled = false;
            adminModeBtn.disabled = false;
            
            // 显示成功消息
            showMessage(`成功生成${count}个卡密`, 'success');
        })
        .catch(error => {
            showMessage(`生成失败: ${error.message}`, 'error');
        })
        .finally(() => {
            // 隐藏进度条
            progressContainer.style.display = 'none';
            
            // 启用生成按钮
            generateBtn.disabled = false;
        });
    });
    
    // 复制信息按钮
    copyBtn.addEventListener('click', function() {
        if (generatedKeys.length === 0) {
            showMessage('没有可复制的内容', 'error');
            return;
        }
        
        // 创建要复制的内容
        let copyContent;
        
        if (isAdminMode) {
            // 管理模式下的复制格式
            const quota = parseInt(quotaInput.value);
            const expires = parseInt(expiresInput.value);
            const count = parseInt(countInput.value);
            
            // 有效期显示处理
            let expiresText = expires === 0 ? "永久" : `${expires}天`;
            
            copyContent = `${expiresText}_${count}个_${quota}次\n`;
            
            // 只复制卡密信息，一行一个
            generatedKeys.forEach((key) => {
                copyContent += `${key.key_hash}\n`;
            });
        } else {
            // 普通模式下的复制格式
            copyContent = "";
            
            // 为每个卡密块提取信息
            generatedKeys.forEach((key, index) => {
                copyContent += `卡密: ${key.key_hash}\n`;
                copyContent += `过期时间: ${convertToChineseTime(key.expires_at)} (中国时间)\n`;
                copyContent += `总配额: ${key.total_quota}\n`;
                copyContent += `剩余配额: ${key.remaining}\n\n`;
            });
            
            // 添加备注信息
            copyContent += "=== 备注信息 ===\n";
            copyContent += " 请妥善保管原始卡密\n\n";
            copyContent += `软件下载地址：\n${downloadInput.value}`;
        }
        
        // 复制到剪贴板
        navigator.clipboard.writeText(copyContent)
            .then(() => {
                showMessage('已复制到剪贴板', 'success');
            })
            .catch(err => {
                console.error("复制失败:", err);
                showMessage('复制失败: ' + err, 'error');
                
                // 备用复制方法
                try {
                    const textarea = document.createElement('textarea');
                    textarea.value = copyContent;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    showMessage('已复制到剪贴板(备用方法)', 'success');
                } catch (e) {
                    showMessage('所有复制方法都失败，请手动复制', 'error');
                }
            });
    });
    
    // 清空日志按钮
    clearBtn.addEventListener('click', function() {
        resultArea.value = '';
        generatedKeys = [];
        copyBtn.disabled = true;
        clearBtn.disabled = true;
        downloadBtn.disabled = true;
        adminModeBtn.disabled = true;
        showMessage('已清空日志', 'info');
    });
    
    // 下载记录按钮
    downloadBtn.addEventListener('click', function() {
        if (generatedKeys.length === 0) {
            showMessage('没有可下载的记录', 'error');
            return;
        }
        
        // 创建要下载的内容
        const quota = parseInt(quotaInput.value);
        const expires = parseInt(expiresInput.value);
        const count = parseInt(countInput.value);
        
        // 有效期显示处理
        let expiresText = expires === 0 ? "永久" : `${expires}天`;
        
        // 创建文件名 - 修改为新格式
        const filename = `${expiresText}_${count}个_${quota}次_.txt`;
        
        // 创建文件内容 - 修改为新格式
        let fileContent = `${expiresText}_${count}个_${quota}次\n`;
        
        // 只写入卡密信息，一行一个
        generatedKeys.forEach((key) => {
            fileContent += `${key.key_hash}\n`;
        });
        
        // 创建下载链接
        const blob = new Blob([fileContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        
        // 清理
        setTimeout(() => {
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }, 0);
        
        showMessage(`已下载记录: ${filename}`, 'success');
    });
    
    // 管理模式按钮
    adminModeBtn.addEventListener('click', function() {
        if (generatedKeys.length === 0) {
            showMessage('没有可切换的内容', 'error');
            return;
        }
        
        // 切换管理模式状态
        isAdminMode = !isAdminMode;
        
        // 更新按钮样式
        if (isAdminMode) {
            adminModeBtn.classList.remove('btn-dark');
            adminModeBtn.classList.add('btn-warning');
            adminModeBtn.textContent = '客户模式';
            showMessage('已切换到管理模式', 'info');
        } else {
            adminModeBtn.classList.remove('btn-warning');
            adminModeBtn.classList.add('btn-dark');
            adminModeBtn.textContent = '管理模式';
            showMessage('已切换到客户模式', 'info');
        }
        
        // 更新显示内容
        const quota = parseInt(quotaInput.value);
        const expires = parseInt(expiresInput.value);
        const count = parseInt(countInput.value);
        const downloadLink = downloadInput.value;
        
        let resultText;
        
        if (isAdminMode) {
            // 管理模式下的显示格式
            let expiresText = expires === 0 ? "永久" : `${expires}天`;
            resultText = `${expiresText}_${count}个_${quota}次\n`;
            
            // 只显示卡密信息，一行一个
            generatedKeys.forEach((key) => {
                resultText += `${key.key_hash}\n`;
            });
        } else {
            // 普通模式下的显示格式
            const currentTime = formatDateTime(new Date());
            resultText = `=== 卡密生成记录 - ${currentTime} ===\n\n`;
            
            // 有效期显示处理
            let expiresText = expires === 0 ? "永久" : `${expires}天`;
            
            resultText += `总配额: ${quota}\n`;
            resultText += `有效期: ${expiresText}\n`;
            resultText += `生成数量: ${count}\n`;
            resultText += "-".repeat(50) + "\n\n";
            
            // 写入卡密信息
            generatedKeys.forEach((key, index) => {
                resultText += `【卡密 ${index + 1}】\n`;
                resultText += `卡密: ${key.key_hash}\n`;
                resultText += `过期时间: ${convertToChineseTime(key.expires_at)}\n`;
                resultText += `总配额: ${key.total_quota}\n`;
                resultText += `剩余配额: ${key.remaining}\n\n`;
            });
            
            // 写入备注信息
            resultText += "=== 备注信息 ===\n";
            resultText += " 请妥善保管原始卡密\n\n";
            resultText += `软件下载地址：\n${downloadLink}\n`;
            resultText += "=".repeat(50);
        }
        
        resultArea.value = resultText;
    });
    
    // 显示消息
    function showMessage(message, type = 'info') {
        // 使用状态栏显示消息
        if (statusMessage) {
            statusMessage.textContent = message;
            statusMessage.className = '';
            switch (type) {
                case 'success':
                    statusMessage.classList.add('text-success');
                    break;
                case 'error':
                    statusMessage.classList.add('text-danger');
                    break;
                case 'warning':
                    statusMessage.classList.add('text-warning');
                    break;
                default:
                    statusMessage.classList.add('text-info');
            }
        }
        
        // 创建一个Toast通知
        const toastContainer = document.getElementById('toastContainer') || createToastContainer();
        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header ${getToastHeaderClass(type)}">
                    <strong class="me-auto">${getToastTitle(type)}</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;
        
        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, { delay: 3000 });
        toast.show();
        
        // 自动移除toast元素
        toastElement.addEventListener('hidden.bs.toast', function () {
            toastElement.remove();
        });
    }
    
    // 创建Toast容器
    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '1050';
        document.body.appendChild(container);
        return container;
    }
    
    // 获取Toast标题
    function getToastTitle(type) {
        switch (type) {
            case 'success': return '成功';
            case 'error': return '错误';
            case 'warning': return '警告';
            default: return '提示';
        }
    }
    
    // 获取Toast头部样式
    function getToastHeaderClass(type) {
        switch (type) {
            case 'success': return 'bg-success text-white';
            case 'error': return 'bg-danger text-white';
            case 'warning': return 'bg-warning';
            default: return 'bg-info text-white';
        }
    }
    
    // 将UTC时间转换为中国时区(UTC+8)
    function convertToChineseTime(utcTimeString) {
        if (!utcTimeString) return '';
        
        // 解析UTC时间字符串
        const utcDate = new Date(utcTimeString.replace(' ', 'T') + 'Z');
        
        // 转换为中国时区 (UTC+8)
        const options = { 
            year: 'numeric', 
            month: '2-digit', 
            day: '2-digit',
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit',
            hour12: false,
            timeZone: 'Asia/Shanghai'
        };
        
        return new Intl.DateTimeFormat('zh-CN', options).format(utcDate);
    }
    
    // 格式化日期时间
    function formatDateTime(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
}); 