document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const quotaInput = document.getElementById('quota');
    const expiresInput = document.getElementById('expires');
    const countInput = document.getElementById('count');
    const downloadInput = document.getElementById('download');
    const generateBtn = document.getElementById('generateBtn');
    const copyBtn = document.getElementById('copyBtn');
    const clearBtn = document.getElementById('clearBtn');
    const downloadBtn = document.getElementById('downloadBtn');
    const adminModeBtn = document.getElementById('adminModeBtn');
    const resultArea = document.getElementById('result');
    const progressContainer = document.getElementById('progressContainer');
    const progressBar = document.getElementById('progressBar');
    const statusMessage = document.getElementById('statusMessage');
    const linkButton = document.getElementById('linkButton');
    
    // 管理模式状态
    let isAdminMode = false;
    
    // 调试信息
    console.log("脚本已加载，当前路径:", window.location.pathname);
    
    // 获取应用根路径
    function getApplicationRoot() {
        // 从当前URL中提取应用根路径
        const path = window.location.pathname;
        console.log("当前URL路径:", path);
        
        // 如果路径是/chaxun/chaxun/开头，返回/chaxun
        if (path === '/chaxun' || path.startsWith('/chaxun/')) {
            console.log("检测到/chaxun");
            return '/chaxun';
        }
        
        // 尝试从第一段路径获取
        const parts = path.split('/').filter(p => p);
        if (parts.length > 0) {
            console.log("从路径提取的第一段:", '/' + parts[0]);
            return '/' + parts[0];
        }
        
        console.log("未检测到路径前缀，使用空前缀");
        return '';
    }
    
    // 构建API URL
    function buildApiUrl(endpoint) {
        const root = getApplicationRoot();
        const url = `${root}${endpoint}`;
        console.log("构建API URL:", url);
        return url;
    }
    
    // 存储生成的密钥
    let generatedKeys = [];
    
    // 配额快捷按钮
    document.querySelectorAll('.quick-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const value = parseInt(this.dataset.value);
            quotaInput.value = value;
            
            // 如果联动开启，设置对应的有效期
            if (linkButton.dataset.linked === 'true') {
                // 配额与有效期的对应关系
                const quotaToExpires = {
                    4: 1,   // 4次对应1天
                    7: 7,   // 7次对应7天
                    30: 30  // 30次对应30天
                };
                
                // 如果当前配额在映射表中，设置对应的有效期
                if (quotaToExpires[value]) {
                    expiresInput.value = quotaToExpires[value];
                }
            }
        });
    });
    
    // 有效期快捷按钮
    document.querySelectorAll('.quick-btn-expires').forEach(btn => {
        btn.addEventListener('click', function() {
            const value = parseInt(this.dataset.value);
            expiresInput.value = value;
            
            // 如果联动开启，设置对应的配额
            if (linkButton.dataset.linked === 'true') {
                // 有效期与配额的对应关系
                const expiresToQuota = {
                    1: 4,   // 1天对应4次
                    3: 7,   // 3天对应7次
                    7: 7,   // 7天对应7次
                    30: 30  // 30天对应30次
                };
                
                // 如果当前有效期在映射表中，设置对应的配额
                if (expiresToQuota[value]) {
                    quotaInput.value = expiresToQuota[value];
                }
            }
        });
    });
    
    // 联动按钮
    linkButton.addEventListener('click', function() {
        const isLinked = this.dataset.linked === 'true';
        this.dataset.linked = isLinked ? 'false' : 'true';
        
        if (this.dataset.linked === 'true') {
            this.classList.add('active');
        } else {
            this.classList.remove('active');
        }
    });
    
    // 生成卡密按钮
    generateBtn.addEventListener('click', function() {
        console.log("点击生成卡密按钮");
        
        const quota = parseInt(quotaInput.value);
        const expires = parseInt(expiresInput.value);
        const count = parseInt(countInput.value);
        const downloadLink = downloadInput.value;
        
        console.log("参数:", { quota, expires, count });
        
        // 参数验证
        if (quota < 1 || quota > 1000000) {
            showStatus('错误：配额必须在1到1000000之间', 'error');
            return;
        }
        
        if (expires < 0 || expires > 3650) {
            showStatus('错误：有效期必须在0到3650天之间', 'error');
            return;
        }
        
        if (count < 1 || count > 1000) {
            showStatus('错误：生成数量必须在1到1000之间', 'error');
            return;
        }
        
        // 禁用按钮，显示进度条
        generateBtn.disabled = true;
        progressContainer.style.display = 'block';
        progressBar.style.width = '0%';
        resultArea.value = '';
        showStatus('正在生成卡密...', 'info');
        
        // 构建请求URL
        const apiUrl = buildApiUrl('/api/generate-keys');
        console.log("发送请求到:", apiUrl);
        
        // 发送API请求
        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                quota: quota,
                expires_days: expires,
                count: count
            }),
            credentials: 'same-origin' // 确保发送cookies
        })
        .then(response => {
            console.log("收到响应:", response.status, response.statusText);
            if (!response.ok) {
                throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log("解析响应数据:", data);
            if (data.success) {
                // 保存生成的密钥
                generatedKeys = data.keys;
                
                // 显示生成的密钥
                let resultText;
                
                if (isAdminMode) {
                    // 管理模式下的显示格式
                    let expiresText = expires === 0 ? "永久" : `${expires}天`;
                    resultText = `${expiresText}_${count}个_${quota}次\n`;
                    
                    // 只显示卡密信息，一行一个
                    data.keys.forEach((key) => {
                        resultText += `${key.key_hash}\n`;
                    });
                } else {
                    // 普通模式下的显示格式
                    const currentTime = data.timestamp;
                    resultText = `=== 卡密生成记录 - ${currentTime} ===\n\n`;
                    
                    // 有效期显示处理
                    let expiresText = expires === 0 ? "永久" : `${expires}天`;
                    
                    resultText += `总配额: ${quota}\n`;
                    resultText += `有效期: ${expiresText}\n`;
                    resultText += `生成数量: ${count}\n`;
                    resultText += "-".repeat(50) + "\n\n";
                    
                    // 写入卡密信息
                    data.keys.forEach((key, index) => {
                        resultText += `【卡密 ${index + 1}】\n`;
                        resultText += `卡密: ${key.key_hash}\n`;
                        resultText += `过期时间: ${key.expires_at}\n`;
                        resultText += `总配额: ${key.total_quota}\n`;
                        resultText += `剩余配额: ${key.remaining}\n\n`;
                    });
                    
                    // 写入备注信息
                    resultText += "=== 备注信息 ===\n";
                    resultText += " 请妥善保管原始卡密\n\n";
                    resultText += `软件下载地址：\n${downloadLink}\n`;
                    resultText += "=".repeat(50);
                }
                
                resultArea.value = resultText;
                
                // 启用复制、清空和管理模式按钮
                copyBtn.disabled = false;
                clearBtn.disabled = false;
                downloadBtn.disabled = false;
                adminModeBtn.disabled = false;
                
                showStatus('卡密生成完成', 'success');
                
                // 模拟进度条完成
                progressBar.style.width = '100%';
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 1000);
            } else {
                showStatus(`错误：${data.error}`, 'error');
            }
        })
        .catch(error => {
            console.error("请求错误:", error);
            showStatus(`错误：${error.message}`, 'error');
        })
        .finally(() => {
            generateBtn.disabled = false;
        });
    });
    
    // 复制信息按钮
    copyBtn.addEventListener('click', function() {
        if (generatedKeys.length === 0) {
            showStatus('没有可复制的内容', 'error');
            return;
        }
        
        // 创建要复制的内容
        let copyContent;
        
        if (isAdminMode) {
            // 管理模式下的复制格式
            const quota = parseInt(quotaInput.value);
            const expires = parseInt(expiresInput.value);
            const count = parseInt(countInput.value);
            
            // 有效期显示处理
            let expiresText = expires === 0 ? "永久" : `${expires}天`;
            
            copyContent = `${expiresText}_${count}个_${quota}次\n`;
            
            // 只复制卡密信息，一行一个
            generatedKeys.forEach((key) => {
                copyContent += `${key.key_hash}\n`;
            });
        } else {
            // 普通模式下的复制格式
            copyContent = "";
            
            // 为每个卡密块提取信息
            generatedKeys.forEach((key, index) => {
                copyContent += `卡密: ${key.key_hash}\n`;
                copyContent += `过期时间: ${key.expires_at}\n`;
                copyContent += `总配额: ${key.total_quota}\n`;
                copyContent += `剩余配额: ${key.remaining}\n\n`;
            });
            
            // 添加备注信息
            copyContent += "=== 备注信息 ===\n";
            copyContent += " 请妥善保管原始卡密\n\n";
            copyContent += `软件下载地址：\n${downloadInput.value}`;
        }
        
        // 复制到剪贴板
        navigator.clipboard.writeText(copyContent)
            .then(() => {
                showStatus('已复制到剪贴板', 'success');
            })
            .catch(err => {
                console.error("复制失败:", err);
                showStatus('复制失败: ' + err, 'error');
                
                // 备用复制方法
                try {
                    const textarea = document.createElement('textarea');
                    textarea.value = copyContent;
                    document.body.appendChild(textarea);
                    textarea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textarea);
                    showStatus('已复制到剪贴板(备用方法)', 'success');
                } catch (e) {
                    showStatus('所有复制方法都失败，请手动复制', 'error');
                }
            });
    });
    
    // 清空日志按钮
    clearBtn.addEventListener('click', function() {
        resultArea.value = '';
        generatedKeys = [];
        copyBtn.disabled = true;
        clearBtn.disabled = true;
        downloadBtn.disabled = true;
        adminModeBtn.disabled = true;
        showStatus('已清空日志', 'info');
    });
    
    // 下载记录按钮
    downloadBtn.addEventListener('click', function() {
        if (generatedKeys.length === 0) {
            showStatus('没有可下载的内容', 'error');
            return;
        }
        
        // 创建要下载的内容
        const quota = parseInt(quotaInput.value);
        const expires = parseInt(expiresInput.value);
        const count = parseInt(countInput.value);
        
        // 有效期显示处理
        let expiresText = expires === 0 ? "永久" : `${expires}天`;
        
        // 创建文件名 - 修改为新格式
        const filename = `${expiresText}_${count}个_${quota}次_.txt`;
        
        // 创建文件内容 - 修改为新格式
        let fileContent = `${expiresText}_${count}个_${quota}次\n`;
        
        // 只写入卡密信息，一行一个
        generatedKeys.forEach((key) => {
            fileContent += `${key.key_hash}\n`;
        });
        
        // 创建下载链接
        const blob = new Blob([fileContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        showStatus(`已下载记录: ${filename}`, 'success');
    });
    
    // 管理模式按钮
    adminModeBtn.addEventListener('click', function() {
        if (generatedKeys.length === 0) {
            showStatus('没有可切换的内容', 'error');
            return;
        }
        
        // 切换管理模式状态
        isAdminMode = !isAdminMode;
        
        // 更新按钮样式
        if (isAdminMode) {
            adminModeBtn.classList.remove('btn-dark');
            adminModeBtn.classList.add('btn-warning');
            adminModeBtn.textContent = '客户模式';
            showStatus('已切换到管理模式', 'info');
        } else {
            adminModeBtn.classList.remove('btn-warning');
            adminModeBtn.classList.add('btn-dark');
            adminModeBtn.textContent = '管理模式';
            showStatus('已切换到客户模式', 'info');
        }
        
        // 更新显示内容
        const quota = parseInt(quotaInput.value);
        const expires = parseInt(expiresInput.value);
        const count = parseInt(countInput.value);
        const downloadLink = downloadInput.value;
        
        let resultText;
        
        if (isAdminMode) {
            // 管理模式下的显示格式
            let expiresText = expires === 0 ? "永久" : `${expires}天`;
            resultText = `${expiresText}_${count}个_${quota}次\n`;
            
            // 只显示卡密信息，一行一个
            generatedKeys.forEach((key) => {
                resultText += `${key.key_hash}\n`;
            });
        } else {
            // 普通模式下的显示格式
            const currentTime = new Date().toLocaleString();
            resultText = `=== 卡密生成记录 - ${currentTime} ===\n\n`;
            
            // 有效期显示处理
            let expiresText = expires === 0 ? "永久" : `${expires}天`;
            
            resultText += `总配额: ${quota}\n`;
            resultText += `有效期: ${expiresText}\n`;
            resultText += `生成数量: ${count}\n`;
            resultText += "-".repeat(50) + "\n\n";
            
            // 写入卡密信息
            generatedKeys.forEach((key, index) => {
                resultText += `【卡密 ${index + 1}】\n`;
                resultText += `卡密: ${key.key_hash}\n`;
                resultText += `过期时间: ${key.expires_at}\n`;
                resultText += `总配额: ${key.total_quota}\n`;
                resultText += `剩余配额: ${key.remaining}\n\n`;
            });
            
            // 写入备注信息
            resultText += "=== 备注信息 ===\n";
            resultText += " 请妥善保管原始卡密\n\n";
            resultText += `软件下载地址：\n${downloadLink}\n`;
            resultText += "=".repeat(50);
        }
        
        resultArea.value = resultText;
    });
    
    // 显示状态消息
    function showStatus(message, type = 'info') {
        console.log(`状态消息: ${type} - ${message}`);
        statusMessage.textContent = message;
        
        // 根据消息类型设置样式
        statusMessage.className = '';
        switch (type) {
            case 'error':
                statusMessage.classList.add('text-danger');
                break;
            case 'success':
                statusMessage.classList.add('text-success');
                break;
            case 'info':
            default:
                statusMessage.classList.add('text-info');
                break;
        }
        
        // 3秒后自动清除消息
        setTimeout(() => {
            statusMessage.textContent = '';
        }, 3000);
    }
    
    // 初始化联动按钮状态
    if (linkButton.dataset.linked === 'true') {
        linkButton.classList.add('active');
    }
    
    // 页面加载完成后显示状态
    showStatus('页面加载完成，可以开始生成卡密', 'info');
}); 